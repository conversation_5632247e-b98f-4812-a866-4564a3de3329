/* Quill Editor Styles */
@import 'quill/dist/quill.snow.css';

/* Custom Quill theme overrides */
.rich-text-editor .ql-toolbar {
  border-top: 1px solid hsl(var(--bc) / 0.2) !important;
  border-left: 1px solid hsl(var(--bc) / 0.2) !important;
  border-right: 1px solid hsl(var(--bc) / 0.2) !important;
  border-bottom: none !important;
  border-radius: 0.5rem 0.5rem 0 0 !important;
  padding: 0.5rem !important;
  background: hsl(var(--b1)) !important;
  font-size: 0.875rem !important;
}

.rich-text-editor .ql-container {
  border-bottom: 1px solid hsl(var(--bc) / 0.2) !important;
  border-left: 1px solid hsl(var(--bc) / 0.2) !important;
  border-right: 1px solid hsl(var(--bc) / 0.2) !important;
  border-top: none !important;
  border-radius: 0 0 0.5rem 0.5rem !important;
  font-size: 0.875rem !important;
}

.rich-text-editor .ql-editor {
  padding: 0.75rem !important;
  line-height: 1.5 !important;
  min-height: 80px !important;
}

.rich-text-editor .ql-editor.ql-blank::before {
  font-style: normal !important;
  color: hsl(var(--bc) / 0.5) !important;
}

.rich-text-editor .ql-toolbar .ql-formats {
  margin-right: 0.5rem !important;
}

.rich-text-editor .ql-toolbar button {
  padding: 0.25rem !important;
  margin: 0.125rem !important;
}

.rich-text-editor .ql-toolbar button:hover {
  background: hsl(var(--b2)) !important;
  border-radius: 0.25rem !important;
}

.rich-text-editor .ql-toolbar button.ql-active {
  background: hsl(var(--p) / 0.1) !important;
  color: hsl(var(--p)) !important;
  border-radius: 0.25rem !important;
}

/* Ensure no duplicate toolbars */
.rich-text-editor .ql-container .ql-toolbar {
  display: none !important;
}

/* Hide any additional toolbar instances */
.rich-text-editor > .ql-toolbar ~ .ql-toolbar {
  display: none !important;
}

.ql-editor {
  padding: 0.75rem !important;
  line-height: 1.5 !important;
  min-height: 60px !important;
  font-size: 0.875rem !important;
  border: 1px solid hsl(var(--bc) / 0.2) !important;
}

.ql-editor.ql-blank::before {
  font-style: normal !important;
  color: hsl(var(--bc) / 0.5) !important;
  font-size: 0.875rem !important;
}

.ql-toolbar .ql-formats {
  margin-right: 0.5rem !important;
}

.ql-toolbar button {
  padding: 0.25rem !important;
  margin: 0.125rem !important;
  width: 28px !important;
  height: 28px !important;
}

.ql-toolbar button:hover {
  background: hsl(var(--b2)) !important;
  border-radius: 0.25rem !important;
}

.ql-toolbar button.ql-active {
  background: hsl(var(--p) / 0.1) !important;
  color: hsl(var(--p)) !important;
  border-radius: 0.25rem !important;
}

.ql-toolbar .ql-picker {
  font-size: 0.75rem !important;
}

.ql-toolbar .ql-picker-label {
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.25rem !important;
}

.ql-toolbar .ql-picker-label:hover {
  background: hsl(var(--b2)) !important;
}

.ql-toolbar .ql-picker.ql-expanded .ql-picker-label {
  background: hsl(var(--p) / 0.1) !important;
  color: hsl(var(--p)) !important;
}

.ql-toolbar .ql-picker-options {
  background: hsl(var(--b1)) !important;
  border: 1px solid hsl(var(--bc) / 0.2) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
}

.ql-toolbar .ql-picker-item {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
}

.ql-toolbar .ql-picker-item:hover {
  background: hsl(var(--b2)) !important;
}

/* Math and formula support */
.ql-editor .ql-formula {
  background: hsl(var(--b2)) !important;
  border: 1px solid hsl(var(--bc) / 0.2) !important;
  border-radius: 0.25rem !important;
  padding: 0.125rem 0.25rem !important;
  font-family: 'Courier New', monospace !important;
}

/* Link styling */
.ql-editor a {
  color: hsl(var(--p)) !important;
  text-decoration: underline !important;
}

.ql-editor a:hover {
  color: hsl(var(--pf)) !important;
}

/* List styling */
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5rem !important;
}

.ql-editor li {
  margin-bottom: 0.25rem !important;
}

/* Subscript and superscript */
.ql-editor .ql-script.ql-sub {
  font-size: 0.75em !important;
  vertical-align: sub !important;
}

.ql-editor .ql-script.ql-super {
  font-size: 0.75em !important;
  vertical-align: super !important;
}

/* Focus styles */
.ql-container.ql-focused {
  border-color: hsl(var(--p)) !important;
  box-shadow: 0 0 0 2px hsl(var(--p) / 0.2) !important;
}

.ql-toolbar.ql-focused {
  border-color: hsl(var(--p)) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ql-toolbar {
    padding: 0.375rem !important;
  }
  
  .ql-toolbar button {
    width: 24px !important;
    height: 24px !important;
    padding: 0.125rem !important;
  }
  
  .ql-toolbar .ql-formats {
    margin-right: 0.25rem !important;
  }
  
  .ql-editor {
    padding: 0.5rem !important;
    font-size: 0.8125rem !important;
  }
}
