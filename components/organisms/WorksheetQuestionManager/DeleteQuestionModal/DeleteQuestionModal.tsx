'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Trash2, Alert<PERSON><PERSON>gle, X, Loader2 } from 'lucide-react';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { handleDeleteQuestionAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface DeleteQuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (questionId: string) => void;
  worksheetId: string;
  question: IWorksheetQuestion;
  className?: string;
}

export const DeleteQuestionModal: React.FC<DeleteQuestionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  question,
  className
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reason, setReason] = useState('');
  const dialogRef = useRef<HTMLDialogElement>(null);

  // Handle dialog open/close with proper DaisyUI pattern
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      const response = await handleDeleteQuestionAction(
        worksheetId,
        question.id,
        reason || undefined
      );

      if (response.status === 'success') {
        onSuccess(question.id);
        handleClose();
      } else {
        setError(response.message || 'Failed to delete question');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setError(null);
      setReason('');
      onClose();
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'MULTIPLE_CHOICE': 'Multiple Choice',
      'TRUE_FALSE': 'True/False',
      'FILL_IN_BLANK': 'Fill in the Blank',
      'SHORT_ANSWER': 'Short Answer',
      'ESSAY': 'Essay',
      'MATCHING': 'Matching',
      'ORDERING': 'Ordering'
    };
    return typeMap[type] || type;
  };

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-error/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-error" />
            </div>
            <h3 className="text-lg font-semibold">Delete Question</h3>
          </div>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-sm btn-circle"
              disabled={isDeleting}
            >
              <X className="w-4 h-4" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
          </div>
        )}

        {/* Question Preview */}
        <div className="bg-base-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <span className="badge badge-outline badge-sm">
              {getQuestionTypeLabel(question.type)}
            </span>
            {question.difficulty && (
              <span className="text-xs text-base-content/70">
                {question.difficulty}
              </span>
            )}
          </div>
          <p className="text-sm text-base-content line-clamp-3">
            {question.content}
          </p>
          {question.options && question.options.length > 0 && (
            <div className="mt-2">
              <div className="text-xs text-base-content/70 mb-1">Options:</div>
              <div className="flex flex-wrap gap-1">
                {question.options.slice(0, 2).map((option, idx) => (
                  <span key={idx} className="badge badge-outline badge-xs">
                    {option.length > 15 ? `${option.substring(0, 15)}...` : option}
                  </span>
                ))}
                {question.options.length > 2 && (
                  <span className="badge badge-ghost badge-xs">
                    +{question.options.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Warning Message */}
        <div className="alert alert-warning mb-4">
          <AlertTriangle className="w-5 h-5" />
          <div>
            <h4 className="font-medium">Are you sure?</h4>
            <p className="text-sm">
              This action cannot be undone. The question will be permanently removed from the worksheet.
            </p>
          </div>
        </div>

        {/* Reason Input */}
        <div className="form-control mb-6">
          <label className="label">
            <span className="label-text">Reason for deletion (optional)</span>
          </label>
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="textarea textarea-bordered h-20"
            placeholder="Provide a reason for deleting this question..."
            disabled={isDeleting}
            maxLength={500}
          />
          <label className="label">
            <span className="label-text-alt">{reason.length}/500 characters</span>
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            onClick={handleClose}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={handleDelete}
            disabled={isDeleting}
            className="btn-error"
          >
            {isDeleting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
            <Trash2 className="w-4 h-4 mr-2" />
            Delete Question
          </Button>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
